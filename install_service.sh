#!/bin/bash

# Install the RespiraSense AI API as a systemd service
# This script must be run with sudo privileges
# Optimized for Raspberry Pi 4B

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Please run this script with sudo"
    exit 1
fi

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

# Get the current directory
CURRENT_DIR=$(pwd)

# Get the current user
CURRENT_USER=$(logname)

# Check if virtual environment exists
if [ ! -d "$CURRENT_DIR/venv" ]; then
    echo "Virtual environment not found. Running setup script..."
    su -c "$CURRENT_DIR/setup_venv.sh" $CURRENT_USER
fi

# Create logs directory if it doesn't exist
mkdir -p $CURRENT_DIR/logs
chown -R $CURRENT_USER:$CURRENT_USER $CURRENT_DIR/logs

# Update the service file with the correct paths and settings
sed -i "s|WorkingDirectory=.*|WorkingDirectory=$CURRENT_DIR|g" respirasense_api.service
sed -i "s|User=.*|User=$CURRENT_USER|g" respirasense_api.service

# Update ExecStart to use venv
EXEC_START="ExecStart=$CURRENT_DIR/venv/bin/gunicorn -w 2 -b 0.0.0.0:5000 --log-level debug --access-logfile $CURRENT_DIR/logs/access.log --error-logfile $CURRENT_DIR/logs/error.log app:app"
sed -i "s|ExecStart=.*|$EXEC_START|g" respirasense_api.service

# Copy the service file to the systemd directory
cp respirasense_api.service /etc/systemd/system/

# Create a symlink to the journal logs for easy access
ln -sf /var/log/syslog $CURRENT_DIR/logs/system.log

# Set appropriate permissions for the logs directory
chown -R $CURRENT_USER:$CURRENT_USER $CURRENT_DIR/logs

# Reload systemd
systemctl daemon-reload

# Enable the service to start on boot
systemctl enable respirasense_api.service

# Start the service
systemctl start respirasense_api.service

echo "RespiraSense AI API service installed and started."
echo "Check status with: sudo systemctl status respirasense_api.service"
echo "Logs are available in the logs/ directory:"
echo "  - Application logs: logs/access.log and logs/error.log"
echo "  - System logs: logs/system.log"
