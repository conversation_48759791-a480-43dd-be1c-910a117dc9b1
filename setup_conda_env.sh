#!/bin/bash

# Setup script for creating and configuring the respirasense.ai conda environment
# This script handles the creation of the conda environment and installation of all dependencies

echo "Setting up RespiraSense AI conda environment..."

# Check if conda is installed
if ! command -v conda &> /dev/null; then
    echo "Conda is not installed. Please install Conda and try again."
    exit 1
fi

# Find conda.sh in a more robust way
CONDA_BASE=$(conda info --base)
if [ -f "${CONDA_BASE}/etc/profile.d/conda.sh" ]; then
    source "${CONDA_BASE}/etc/profile.d/conda.sh"
elif [ -f "${HOME}/.conda/etc/profile.d/conda.sh" ]; then
    source "${HOME}/.conda/etc/profile.d/conda.sh"
elif [ -f "/opt/conda/etc/profile.d/conda.sh" ]; then
    source "/opt/conda/etc/profile.d/conda.sh"
else
    echo "Could not find conda.sh. Please run 'conda init bash' and try again."
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Remove existing environment if it exists and --force is specified
if [ "$1" == "--force" ]; then
    echo "Removing existing environment (if it exists)..."
    conda env remove -n respirasense.ai
fi

# Check if conda environment exists, if not create one
if ! conda env list | grep -q "respirasense.ai"; then
    echo "Creating conda environment 'respirasense.ai'..."
    conda create -y -n respirasense.ai python=3.9
    echo "Conda environment created."
else
    echo "Conda environment 'respirasense.ai' already exists."
fi

# Activate the environment
echo "Activating conda environment..."
conda activate respirasense.ai

# Install base dependencies with conda
echo "Installing base dependencies with conda..."
conda install -y -n respirasense.ai -c conda-forge numpy=1.24.3 scipy=1.11.3 setuptools=68.0.0 wheel=0.40.0

# Install pip dependencies in batches to avoid dependency conflicts
echo "Installing Flask and related dependencies..."
conda run -n respirasense.ai pip install flask==2.3.3 flask-cors==4.0.0 gunicorn==21.2.0

echo "Installing ML dependencies..."
conda run -n respirasense.ai pip install tensorflow==2.13.0 joblib==1.3.2

echo "Installing Bluetooth dependencies..."
conda run -n respirasense.ai pip install bleak==0.21.1

# Create empty __init__.py files in all required directories
echo "Creating __init__.py files in code directories..."
mkdir -p code code/body_temp code/breathing_rate code/spirometry code/max30100 code/buzzer code/whispering_pectoriloquy code/oximeter_hack model
touch code/__init__.py code/body_temp/__init__.py code/breathing_rate/__init__.py code/spirometry/__init__.py code/max30100/__init__.py code/buzzer/__init__.py code/whispering_pectoriloquy/__init__.py code/oximeter_hack/__init__.py model/__init__.py

# Try to install Adafruit libraries (these might fail on non-Raspberry Pi systems)
echo "Installing Adafruit libraries (may fail on non-Raspberry Pi systems)..."
conda run -n respirasense.ai pip install adafruit-circuitpython-mlx90614==1.3.8 adafruit-circuitpython-mpu6050==1.1.16 adafruit-circuitpython-adxl34x==1.12.3 || echo "Warning: Could not install Adafruit libraries. This is expected on non-Raspberry Pi systems."

# Print environment information
echo "Conda environment 'respirasense.ai' setup complete."
echo "Python version:"
conda run -n respirasense.ai python --version
echo "Installed packages:"
conda run -n respirasense.ai pip list

echo "To activate the environment, run: conda activate respirasense.ai"
echo "To start the server, run: ./start_server.sh"
