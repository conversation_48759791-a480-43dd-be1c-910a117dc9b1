#!/bin/bash

# Start the RespiraSense AI API server
# This script can be used to start the server in development or production mode
# Optimized for Raspberry Pi 4B

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

# Run the setup script to ensure the environment is properly configured
if [ ! -f "./setup_venv.sh" ]; then
    echo "Error: setup_venv.sh not found. Please make sure you're in the correct directory."
    exit 1
fi

# Check if virtual environment needs to be set up
if [ ! -d "venv" ]; then
    echo "Virtual environment not found. Running setup script..."
    ./setup_venv.sh
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Check if __init__.py files exist in required directories
for dir in hardware_code hardware_code/body_temp hardware_code/spirometry hardware_code/max30100 hardware_code/buzzer hardware_code/whispering_pectoriloquy hardware_code/oximeter_hack model; do
    if [ -d "$dir" ] && [ ! -f "$dir/__init__.py" ]; then
        echo "Creating missing __init__.py in $dir"
        touch "$dir/__init__.py"
    fi
done

# Check if running in production mode
if [ "$1" == "prod" ]; then
    echo "Starting server in production mode..."
    gunicorn -w 4 -b 0.0.0.0:5000 --log-level info --access-logfile logs/access.log --error-logfile logs/error.log app:app
else
    echo "Starting server in development mode..."
    python app.py
fi
