[Unit]
Description=RespiraSense AI API Server
After=network.target

[Service]
User=pi
WorkingDirectory=/home/<USER>/respirasense_ai
ExecStart=/home/<USER>/respirasense_ai/venv/bin/gunicorn -w 2 -b 0.0.0.0:5000 --log-level debug --access-logfile /home/<USER>/respirasense_ai/logs/access.log --error-logfile /home/<USER>/respirasense_ai/logs/error.log app:app
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=respirasense_api
Environment=PYTHONUNBUFFERED=1

# Resource limits for Raspberry Pi
CPUQuota=80%
MemoryLimit=500M

[Install]
WantedBy=multi-user.target
