mir-spirometer-ble/
├── docs/
│   └── Characteristics.md            # Documentation of BLE characteristics & protocol
├── examples/
│   └── read_data.py                  # Example script to connect and read spirometry data
├── src/
│   └── mir_ble/
│       ├── __init__.py
│       ├── decoder.py                # Core decoding logic for data bytes from device
│       ├── device.py                 # Device connection & BLE communication logic
│       ├── exceptions.py             # Custom exceptions for error handling
│       └── utils.py                  # Helper utilities for BLE communication
├── .gitignore
├── LICENSE
├── README.md
├── requirements.txt                 # Python dependencies (e.g., bleak)


Characteristics.md
MIR Smart One BLE Characteristics Overview
The device exposes several BLE GATT characteristics, each corresponding to specific measurement data or control commands.

UUID	Name	Properties	Description
7f04f3f0-b665-11e3-a5e2-0800200c9a66	Main Service		Primary service containing measurement and control chars
92b403f0-b665-11e3-a5e2-0800200c9a66	Command Char	Write, Write Without Resp.	Write commands to control the device (start, stop, unlock)
f9f84150-b667-11e3-a5e2-0800200c9a66	FEV1	Read, Notify	Forced Expiratory Volume in 1 second
f9f84151-b667-11e3-a5e2-0800200c9a66	FVC	Read, Notify	Forced Vital Capacity
f9f84152-b667-11e3-a5e2-0800200c9a66	FEV1/FVC Ratio	Read, Notify	Ratio of FEV1 to FVC, expressed as a percentage
f9f84153-b667-11e3-a5e2-0800200c9a66	PEF	Read, Notify	Peak Expiratory Flow
7d32c0f0-bef5-11e3-b1b6-0800200c9a66	Status	Read, Notify	Current device status (Idle, Measuring, Complete, etc.)
2d417c80-b667-11e3-a5e2-0800200c9a66	Device Info	Read	Device firmware and hardware info
1dcec130-b668-11e3-a5e2-0800200c9a66	Battery Level	Read	Battery percentage

Command Characteristic Commands
Command (hex)	Description
01 00 00 00 00 00	Enable measurement mode
02 00 00 00 00 00	Request last stored result
00 00 00 00 00 00	Clear device state
1b 00 00 00 00 00	Unlock device from protected mode

Data Formats
FEV1, FVC, PEF:
Sent as unsigned 16-bit integers (2 bytes) in little-endian order.
Units:

FEV1 and FVC in liters × 100 (e.g., value 1234 = 12.34 L)

PEF in liters per second × 10 (e.g., value 300 = 30.0 L/s)

FEV1/FVC Ratio:
1 or 2 bytes representing percentage value (e.g., 75 = 75%)

Status:
1 byte representing device state:

0x00: Idle

0x01: Measuring

0x02: Test complete

Typical Notification Packet Examples
FEV1: d2 04 → 0x04d2 = 1234 → 12.34 L

PEF: 58 02 → 0x0258 = 600 → 60.0 L/s

Status: 02 → Test complete