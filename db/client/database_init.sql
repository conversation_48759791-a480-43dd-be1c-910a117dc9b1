/*
Author: <PERSON><PERSON>
v=1.0
*/

/*Creating Tables*/

/*Patients*/
DROP TABLE IF EXISTS patients;
CREATE TABLE patients(
  patientID INT PRIMARY KEY,
  age INT,
  weight FLOAT(5,2),
  height FLOAT(5,2),
  gender BOOLEAN
);

/*Tests*/
DROP TABLE IF EXISTS tests;
CREATE TABLE tests(
  time TIMESTAMP,
  patientID INT,
  temp FLOAT(4,2), 
  spo2 FLOAT(5,2),
  heart_rate INT,
  tidal_vol FLOAT(5,2)
);

/*Relationships*/
ALTER TABLE tests ADD CONSTRAINT fk_tests_patients FOREIGN KEY (patientID) REFERENCES patients(patientID) ; 