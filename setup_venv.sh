#!/bin/bash

# Setup script for creating and configuring a Python virtual environment
# Optimized for Raspberry Pi 4B

echo "Setting up RespiraSense AI virtual environment..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Remove existing environment if it exists and --force is specified
if [ "$1" == "--force" ] && [ -d "venv" ]; then
    echo "Removing existing virtual environment..."
    rm -rf venv
fi

# Check if virtual environment exists, if not create one
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
    echo "Virtual environment created."
else
    echo "Virtual environment already exists."
fi

# Activate the environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install base dependencies
echo "Installing base dependencies..."
pip install wheel setuptools

# Install Flask and related dependencies
echo "Installing Flask and related dependencies..."
pip install flask==2.3.3 flask-cors==4.0.0 gunicorn==21.2.0

# Install ML dependencies (lightweight versions for Raspberry Pi)
echo "Installing ML dependencies (optimized for Raspberry Pi)..."
pip install numpy==1.24.3 scipy==1.11.3 joblib==1.3.2

# Try to install TensorFlow Lite (more suitable for Raspberry Pi)
echo "Installing TensorFlow Lite (optimized for Raspberry Pi)..."
if pip install tflite-runtime; then
    echo "TensorFlow Lite installed successfully"
else
    echo "TensorFlow Lite installation failed, falling back to regular TensorFlow"
    echo "Installing TensorFlow (this may take a while on Raspberry Pi)..."
    pip install tensorflow==2.9.1
fi

# Install Bluetooth dependencies
echo "Installing Bluetooth dependencies..."
pip install bleak==0.21.1

# Install Raspberry Pi specific libraries
echo "Installing Raspberry Pi specific libraries..."
if [ -e /proc/device-tree/model ] && grep -q "Raspberry Pi" /proc/device-tree/model; then
    echo "Detected Raspberry Pi, installing Pi-specific libraries..."

    # Check if Python development headers are installed
    if ! dpkg -l | grep -q "python3-dev"; then
        echo "Python development headers not found. You need to install them with:"
        echo "sudo apt-get update"
        echo "sudo apt-get install -y python3-dev"
        echo "Then run this script again."
        echo ""
        echo "Would you like to install them now? (y/n)"
        read -r answer
        if [ "$answer" = "y" ] || [ "$answer" = "Y" ]; then
            echo "Installing Python development headers..."
            sudo apt-get update
            sudo apt-get install -y python3-dev
        else
            echo "Skipping installation of Python development headers."
            echo "Some libraries may not work correctly."
        fi
    fi

    # Try to install RPi.GPIO from piwheels (pre-built wheels for Raspberry Pi)
    echo "Installing RPi.GPIO from piwheels..."
    pip install --extra-index-url https://www.piwheels.org/simple RPi.GPIO

    # Install Adafruit libraries with latest available versions
    echo "Installing Adafruit libraries..."
    pip install adafruit-blinka
    pip install adafruit-circuitpython-mlx90614 adafruit-circuitpython-mpu6050 adafruit-circuitpython-adxl34x
else
    echo "Not running on a Raspberry Pi, skipping Pi-specific libraries"
    # Install mock libraries for development on non-Pi systems
    mkdir -p mock_libraries

    # Create mock RPi.GPIO
    if [ ! -d "mock_libraries/RPi" ]; then
        mkdir -p mock_libraries/RPi
        cat > mock_libraries/RPi/GPIO.py << EOF
# Mock RPi.GPIO module for development
print("Using mock RPi.GPIO module")
BCM = 11
BOARD = 10
OUT = 1
IN = 0
HIGH = 1
LOW = 0
PUD_UP = 22
PUD_DOWN = 21

def setmode(*args, **kwargs): pass
def setup(*args, **kwargs): pass
def output(*args, **kwargs): pass
def input(*args, **kwargs): return 0
def cleanup(*args, **kwargs): pass
def setwarnings(*args, **kwargs): pass
EOF
    fi

    # Add mock libraries to Python path
    export PYTHONPATH=$PYTHONPATH:$(pwd)/mock_libraries
    echo "export PYTHONPATH=$PYTHONPATH:$(pwd)/mock_libraries" >> venv/bin/activate
fi

# Create empty __init__.py files in all required directories
echo "Creating __init__.py files in hardware_code directories..."
mkdir -p hardware_code hardware_code/body_temp hardware_code/spirometry hardware_code/max30100 hardware_code/buzzer hardware_code/whispering_pectoriloquy hardware_code/oximeter_hack model
touch hardware_code/__init__.py hardware_code/body_temp/__init__.py hardware_code/spirometry/__init__.py hardware_code/max30100/__init__.py hardware_code/buzzer/__init__.py hardware_code/whispering_pectoriloquy/__init__.py hardware_code/oximeter_hack/__init__.py model/__init__.py

# Print environment information
echo "Virtual environment setup complete."
echo "Python version:"
python --version
echo "Installed packages:"
pip list

echo "To activate the environment, run: source venv/bin/activate"
echo "To start the server, run: ./start_server.sh"

# Deactivate the virtual environment
deactivate
