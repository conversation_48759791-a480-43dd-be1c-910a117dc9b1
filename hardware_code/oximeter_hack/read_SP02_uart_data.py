# A python file that reades hacked values from a Yonker YK-80A oximeter
# The UART interfate has the follwing characteristics

# UART DATA STREAM
# 57600 Baud
# 8 Data Bits
# 1 Stop Bit
# no parity
# After a value of 129, The Sp02 value is read, then the heart rate is next


import serial
import sys

OXIMETER_READ_COUNT = 3


# Returns two data values, sp02 and heart rate data
def read_oximeter_data():
    ser = serial.Serial(
        port='/dev/ttyS0',
        baudrate=57600,
        bytesize=8,
        parity=serial.PARITY_NONE,
        stopbits=serial.STOPBITS_ONE,
        timeout=1
    )

    try:
        i = 0
        readings = []

        while i < OXIMETER_READ_COUNT:
            data = ser.read(1)

            if data == b'\x81':
                # Read spo2 and heartrate, ignore bar graph reading
                values = ser.read(2)
                sp02, heart_rate = values
                if (sp02 == 0 or heart_rate == 0 or sp02 == heart_rate):
                    continue
                readings.append((sp02, heart_rate))
                i += 1

        sp02_average = sum(sp02 for sp02, _ in readings) / OXIMETER_READ_COUNT
        heart_rate_average = sum(
            heart_rate for _, heart_rate in readings) / OXIMETER_READ_COUNT

        return sp02_average, heart_rate_average

    except Exception as e:
        print(e)
        sys.exit(3)


if __name__ == "__main__":
    sp02, heart_rate = read_oximeter_data()
    print(f"SP02 {sp02}%\theart_rate {heart_rate}bpm")
