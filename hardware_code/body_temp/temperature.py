# Exports the temperature value read from the infrared MLX90614 temperature sensor
# NOTE TO SELF : The temprature requires calibration, it reads values too low

import board
import busio as io
import adafruit_mlx90614
import sys

from time import sleep

i2c = io.I2C(board.SCL, board.SDA, frequency=100000)
mlx = adafruit_mlx90614.MLX90614(i2c)

CALIBRATION_DELTA = 6.7
MINIMUM_VALUE = 100


def read_temperature():
    try:
        temp = "{:.2f}".format(mlx.object_temperature + CALIBRATION_DELTA)
        return temp
    except Exception as e:
        print(e)
        return -275


if __name__ == "__main__":
    try:
        while True:
            t = read_temperature()

            # IO error, exit the program
            if t == -275:
                sys.exit(2)
            print(f"Temp = {t}C")

    except KeyboardInterrupt:
        sys.exit()
