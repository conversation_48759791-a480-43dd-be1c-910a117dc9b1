# mir_ble/exceptions.py

class MirSpiroError(Exception):
    """Base exception for MIR Spirometer errors."""
    pass


class DeviceNotFoundError(MirSpiroError):
    """Raised when the MIR Smart One device cannot be found during scanning."""
    pass


class ConnectionError(MirSpiroError):
    """Raised when connection to the device fails."""
    pass


class PairingError(MirSpiroError):
    """Raised when BLE pairing or bonding fails."""
    pass


class NotificationError(MirSpiroError):
    """Raised when subscribing to notifications fails."""
    pass


class CommandError(MirSpiroError):
    """Raised when sending commands to the device fails."""
    pass
