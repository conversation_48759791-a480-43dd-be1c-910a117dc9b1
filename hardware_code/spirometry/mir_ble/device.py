# mir_ble/device.py

import asyncio
from bleak import BleakClient, BleakScanner
from mir_ble.decoder import MirSpiroDecoder
from mir_ble.exceptions import (
    ConnectionError,
    NotificationError,
    MirSpiroError,
)


class MirSmartOneDevice:
    def __init__(self, device_address: str):
        self.device_address = device_address
        self.client = None

        self.UUIDS = {
            "COMMAND": "92b403f0-b665-11e3-a5e2-0800200c9a66",
            "FEV1": "f9f84150-b667-11e3-a5e2-0800200c9a66",
            "FVC": "f9f84151-b667-11e3-a5e2-0800200c9a66",
            "FEV1_FVC": "f9f84152-b667-11e3-a5e2-0800200c9a66",
            "PEF": "f9f84153-b667-11e3-a5e2-0800200c9a66",
            "STATUS": "7d32c0f0-bef5-11e3-b1b6-0800200c9a66",
        }

        self.ENABLE_MEASUREMENT = bytes.fromhex("01 00 00 00 00 00")
        self.UNLOCK_COMMAND = b'\x1b\x00'

    async def connect(self):
        device = await BleakScanner.find_device_by_address(self.device_address, timeout=20.0)
        if device is None:
            raise DeviceConnectionError(
                f"Device {self.device_address} not found during scan")

        self.client = BleakClient(device)
        await self.client.connect()

    async def start_notifications(self):
        if not self.client or not self.client.is_connected:
            raise NotificationStartError("Client not connected")

        for name, uuid in self.UUIDS.items():
            if name == "COMMAND":
                continue
            await self.client.start_notify(uuid, self._make_notification_handler(name))

    def _make_notification_handler(self, name):
        def handler(sender, data):
            if name == "FEV1":
                val = MirSpiroDecoder.decode_fev1(data)
                print(f"FEV1: {val} L")
            elif name == "FVC":
                val = MirSpiroDecoder.decode_fvc(data)
                print(f"FVC: {val} L")
            elif name == "FEV1_FVC":
                val = MirSpiroDecoder.decode_fev1_fvc_ratio(data)
                print(f"FEV1/FVC Ratio: {val}%")
            elif name == "PEF":
                val = MirSpiroDecoder.decode_pef(data)
                print(f"PEF: {val} L/s")
            elif name == "STATUS":
                val = MirSpiroDecoder.decode_status(data)
                print(f"Status: {val}")
        return handler

    async def send_command(self, command: bytes):
        if not self.client or not self.client.is_connected:
            raise MeasurementCommandError("Client not connected")
        await self.client.write_gatt_char(self.UUIDS["COMMAND"], command, response=False)

    async def stop_notifications(self):
        if not self.client or not self.client.is_connected:
            return
        for name, uuid in self.UUIDS.items():
            if name == "COMMAND":
                continue
            try:
                await self.client.stop_notify(uuid)
            except Exception:
                pass

    async def disconnect(self):
        if self.client and self.client.is_connected:
            await self.client.disconnect()
