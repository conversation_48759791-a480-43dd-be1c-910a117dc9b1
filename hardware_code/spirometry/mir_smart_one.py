import asyncio
from mir_ble.device import MirSmartOneDevice

# Replace with your own device's MAC address
DEVICE_MAC = "CF:C3:A0:A2:BE:15"


async def main():
    device = MirSmartOneDevice(DEVICE_MAC)
    try:
        await device.connect()
        print("Connected to MIR Smart One")

        await device.start_notifications()
        print("Notifications started")

        await device.send_command(device.UNLOCK_COMMAND)
        print("Device unlocked")

        await asyncio.sleep(1)

        await device.send_command(device.ENABLE_MEASUREMENT)
        print("Measurement enabled — blow into the spirometer...")

        await asyncio.sleep(10)  # wait for measurements

    finally:
        await device.stop_notifications()
        await device.disconnect()
        print("Disconnected and stopped notifications.")

if __name__ == "__main__":
    asyncio.run(main())
