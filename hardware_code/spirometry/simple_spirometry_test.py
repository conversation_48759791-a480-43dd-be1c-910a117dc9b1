import asyncio
from bleak import BleakClient, BleakScanner
import struct
import time

DEVICE_ADDRESS = "CF:C3:A0:A2:BE:15"  # Replace with your actual device address

# Characteristic UUIDs
COMMAND_CHAR = "92b403f0-b665-11e3-a5e2-0800200c9a66"
FEV1_CHAR = "f9f84150-b667-11e3-a5e2-0800200c9a66"
FVC_CHAR = "f9f84151-b667-11e3-a5e2-0800200c9a66"
PEF_CHAR = "f9f84153-b667-11e3-a5e2-0800200c9a66"
RATIO_CHAR = "f9f84152-b667-11e3-a5e2-0800200c9a66"
STATUS_CHAR = "7d32c0f0-bef5-11e3-b1b6-0800200c9a66"

results = {"FEV1": None, "FVC": None, "PEF": None, "FEV1/FVC": None}
test_completed = asyncio.Event()


def handler(label):
    def inner(sender, data):
        if data.hex() == "ffff":
            print(f"{label}: (invalid)")
            return
        value = None
        if len(data) == 1:
            value = data[0] / 10.0
        elif len(data) == 2:
            value = struct.unpack('<H', data)[0] / 100.0
        elif len(data) == 4:
            value = struct.unpack('<f', data)[0]
        if value is not None:
            print(f"{label}: {value}")
            results[label] = value
    return inner


def status_handler(sender, data):
    if data and data[0] == 2:
        print("✅ Test complete")
        test_completed.set()


async def run_single_test(client):
    global test_completed
    test_completed.clear()
    results.update({k: None for k in results})

    print("\n🫁 Get ready to blow. Deep breath in!")
    await asyncio.sleep(1)
    print("3...")
    await asyncio.sleep(1)
    print("2...")
    await asyncio.sleep(1)
    print("1...")
    await asyncio.sleep(1)
    print("💨 BLOW HARD AND FAST NOW!")

    await client.write_gatt_char(COMMAND_CHAR, b'\x01')
    try:
        await asyncio.wait_for(test_completed.wait(), timeout=10)
    except asyncio.TimeoutError:
        print("⚠️ No test completion signal received.")

    await asyncio.sleep(2)
    print("\n📊 Results:")
    for k, v in results.items():
        print(f"{k}: {v if v else 'No data'}")
    return results


async def simple_spirometry():
    print("🔍 Scanning for MIR Smart One...")
    device = await BleakScanner.find_device_by_address(DEVICE_ADDRESS, timeout=10)
    if not device:
        print("❌ Device not found.")
        return

    async with BleakClient(device) as client:
        print("✅ Connected to device.")

        # Unlock if necessary
        status = await client.read_gatt_char(STATUS_CHAR)
        if status.startswith(b'\x1b'):
            await client.write_gatt_char(COMMAND_CHAR, b'\x1b\x00')
            await asyncio.sleep(1)
            print("🔓 Unlock sent")

        # Enable notifications
        await client.start_notify(FEV1_CHAR, handler("FEV1"))
        await client.start_notify(FVC_CHAR, handler("FVC"))
        await client.start_notify(PEF_CHAR, handler("PEF"))
        await client.start_notify(RATIO_CHAR, handler("FEV1/FVC"))
        await client.start_notify(STATUS_CHAR, status_handler)

        # Clear previous state
        await client.write_gatt_char(COMMAND_CHAR, b'\x00')
        await asyncio.sleep(1)
        await client.write_gatt_char(COMMAND_CHAR, b'\x02')
        await asyncio.sleep(1)
        await client.write_gatt_char(COMMAND_CHAR, b'\x00')
        await asyncio.sleep(1)

        print("\n🧪 Beginning 3 test attempts. Use strongest, fastest blows.")
        best_pef = 0
        best_result = {}

        for i in range(3):
            print(f"\n=== Attempt {i+1} ===")
            result = await run_single_test(client)
            if result['PEF'] and result['PEF'] > best_pef:
                best_pef = result['PEF']
                best_result = result
            await asyncio.sleep(3)

        print("\n✅ Best Result:")
        for k, v in best_result.items():
            print(f"{k}: {v if v else 'No data'}")

if __name__ == "__main__":
    asyncio.run(simple_spirometry())
