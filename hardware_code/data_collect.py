# A program that collects sensor data for the machine learning model

############################

# Author: <PERSON><PERSON>
# email:  <EMAIL>

#############################

# GENERAL DATA
# weight
# height
# gender
# age


# SENSOR DATA
# Accelerometer
# Body Temperature
# Sp02
# Heart Rate
# Spirometry

# General data

class Patient:
    def __init__(self, patient_id: int, height: float, weight: float, gender: bool, age: int):
        self.patient_id = patient_id
        self.height = height
        self.weight = weight
        self.gender = gender
        self.age = age

    # Prints out a patients basic data
    def describe(self):
        print(
            f"Patient {self.patient_id}\nHeight {self.height}\nWeight {self.weight}\nGender {self.gender}\nAge {self.age}")


if __name__ == "__main__":
    print("Enter data parameters as instructed")
    p = Patient(123, 170, 65, 1, 25)
    p.describe()
