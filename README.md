# RespiraSense AI API

A Flask-based REST API for the RespiraSense AI system, providing endpoints for sensor data collection and COPD prediction. Optimized for Raspberry Pi 4B.

## Features

- Body temperature measurement via MLX90614 sensor
- Spirometry testing via MIR Smart One Bluetooth device
- COPD risk prediction using TensorFlow Lite for efficient inference on Raspberry Pi
- Optimized resource usage for Raspberry Pi 4B

## Setup

### Prerequisites

- Raspberry Pi 4B with Raspberry Pi OS (Bullseye or newer)
- Python 3.9+ installed
- Python development headers (`sudo apt-get install python3-dev`)
- The following sensors connected to the Raspberry Pi:
  - MLX90614 temperature sensor (I2C)
  - MIR Smart One spirometer (Bluetooth)
  - MAX30100 pulse oximeter (I2C)

#### Required System Packages

Before running the setup script, install these system packages:

```bash
sudo apt-get update
sudo apt-get install -y python3-dev python3-pip libatlas-base-dev
```

These packages are needed to build the NumPy and sensor libraries.

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/respirasense_ai.git
   cd respirasense_ai
   ```

2. Set up the Python virtual environment:
   ```
   chmod +x setup_venv.sh
   ./setup_venv.sh
   ```
   This will:
   - Create a Python virtual environment in the `venv` directory
   - Install all required dependencies optimized for Raspberry Pi
   - Set up TensorFlow Lite for efficient model inference
   - Create the logs directory and necessary package structure

   If you need to recreate the environment from scratch, use:
   ```
   ./setup_venv.sh --force
   ```

3. Ensure the model files are in the correct location:
   - `model/copd_model.h5`
   - `model/copd_scaler.pkl`

   The first time you run the application, it will automatically convert the model to TensorFlow Lite format for better performance on Raspberry Pi.

### Running the API Server

For development mode:
```
chmod +x start_server.sh
./start_server.sh
```

For production mode:
```
./start_server.sh prod
```

The server will run on `http://0.0.0.0:5000` by default.

### Installing as a System Service

To install the API as a system service that starts automatically on boot:

```
sudo chmod +x install_service.sh
sudo ./install_service.sh
```

This will:
- Create a systemd service using the Python virtual environment
- Configure detailed logging with rotation (to prevent SD card wear)
- Set resource limits appropriate for Raspberry Pi 4B
- Start the service automatically
- Enable the service to start on boot

#### Raspberry Pi Optimizations

The service is configured with the following optimizations for Raspberry Pi:
- Uses 2 worker processes instead of 4 to reduce memory usage
- Sets a CPU quota of 80% to prevent system overload
- Sets a memory limit of 500MB to prevent swapping
- Uses TensorFlow Lite for efficient model inference
- Implements log rotation with smaller file sizes

## API Endpoints

### Health Check

```
GET /health
```

Returns the health status of the API and whether the model is loaded.

### Temperature Reading

```
GET /temperature
```

Returns the current body temperature reading from the MLX90614 sensor.



### Spirometry Test

```
GET /spirometry
POST /spirometry
```

Performs a spirometry test using the MIR Smart One device and returns the results.

#### Enhanced Features

- **Dynamic Test Detection**: Automatically detects when the user starts and stops blowing, eliminating fixed timeouts
- **Demographic-Based Interpretation**: Calculates predicted values and traffic light zones based on patient demographics
- **Traffic Light Assessment**: Green (≥80%), Yellow (50-79%), Red (<50%) zones for clinical interpretation

#### GET Request with Query Parameters

```
GET /spirometry?age=45&height_cm=170&gender=male&weight_kg=75
```

#### POST Request with Demographics

```json
{
  "age": 45,
  "height_cm": 170,
  "gender": "male",
  "weight_kg": 75
}
```

#### Response with Interpretation

```json
{
  "PEF": 4.2,
  "FEV1": 3.1,
  "FVC": 4.0,
  "FEV1/FVC": 77.5,
  "interpretation": {
    "predicted_values": {
      "PEF": 420.5,
      "FEV1": 3.45,
      "FVC": 4.12
    },
    "percentages": {
      "PEF": 89.9,
      "FEV1": 89.9,
      "FVC": 97.1
    },
    "zones": {
      "PEF": "Green",
      "FEV1": "Green",
      "FVC": "Green"
    },
    "overall_assessment": "Good - Within normal range"
  }
}
```

### COPD Prediction

```
POST /predict
```

Predicts COPD risk based on provided patient data.

#### Request Body

```json
{
  "age": 65,
  "gen": 1,
  "ht": 165,
  "wt": 70,
  "smoke_hist": 1,
  "smoke_rt": 15,
  "fev1": 1.8,
  "fvc": 2.9,
  "pefr": 280,
  "hr": 82,
  "spo2": 94,
  "temp": 36.8,
  "wheeze": 1,
  "cough": 1,
  "sob": 1,
  "sputum": 1,
  "pectoriloquy": 0,
  "exp_biomass": 0,
  "exp_occ": 1,
  "fam_hist": 0
}
```

#### Response

```json
{
  "probability": 0.35,
  "diagnosis": "Negative",
  "confidence": "Moderate"
}
```

## Client Example

A sample client script is provided to demonstrate how to interact with the API:

```
python client_example.py
```

## Logging

The RespiraSense AI API includes comprehensive logging optimized for Raspberry Pi:

### Log Files

- **Application Logs**:
  - `logs/respirasense_api.log` - Main application log with INFO level and above (rotated, 5MB max)
  - `logs/debug.log` - Detailed debug log with DEBUG level and above (rotated, 5MB max)

- **Server Logs** (when running with Gunicorn):
  - `logs/access.log` - HTTP access logs (rotated)
  - `logs/error.log` - Server error logs (rotated)

- **System Logs** (when installed as a service):
  - Available through journalctl

### Viewing Logs

1. **Application Logs**:
   ```
   cat logs/respirasense_api.log
   ```
   or for real-time monitoring:
   ```
   tail -f logs/respirasense_api.log
   ```

2. **Service Logs** (when installed as a systemd service):
   ```
   sudo journalctl -u respirasense_api.service
   ```
   or for real-time monitoring:
   ```
   sudo journalctl -u respirasense_api.service -f
   ```

3. **All Logs in One Command** (when installed as a service):
   ```
   sudo journalctl -u respirasense_api.service | grep -v "INFO" # Show only warnings and errors
   ```

### Log Rotation

To prevent excessive SD card wear on the Raspberry Pi, all logs are automatically rotated:

- Each log file is limited to 5MB maximum size
- Main application log keeps 3 backup files
- Debug log keeps 2 backup files
- Server logs are rotated by Gunicorn

This ensures that logs don't consume too much space on the Raspberry Pi's SD card while still providing comprehensive debugging information.

## Troubleshooting

### Common Issues

- **Import Errors**: If you see errors like `No module named 'code.body_temp'`, make sure all the required `__init__.py` files are present. Run `./setup_venv.sh` to create them automatically.

- **Virtual Environment Issues**: If you have problems with the virtual environment, try recreating it from scratch with `./setup_venv.sh --force`.

- **Package Installation Errors**: If you encounter errors installing packages:

  1. Make sure you have the required system packages:
     ```
     sudo apt-get update
     sudo apt-get install -y python3-dev python3-pip libatlas-base-dev
     ```

  2. Try installing packages individually:
     ```
     source venv/bin/activate
     pip install flask==2.3.3
     pip install flask-cors==4.0.0
     # etc.
     ```

  3. For RPi.GPIO errors:
     ```
     sudo apt-get install -y python3-rpi.gpio
     ```

  4. For Adafruit library errors, try installing without version constraints:
     ```
     pip install adafruit-circuitpython-mlx90614
     ```

- **Sensor Detection**: If sensors are not detected, check your I2C connections and ensure the correct permissions are set:
  ```
  # Enable I2C interface
  sudo raspi-config
  # Navigate to: Interface Options > I2C > Enable

  # Check I2C devices
  sudo i2cdetect -y 1
  ```

- **Bluetooth Connectivity**: For Bluetooth connectivity issues with the spirometer:
  ```
  # Check Bluetooth status
  sudo systemctl status bluetooth

  # Restart Bluetooth if needed
  sudo systemctl restart bluetooth

  # Make sure the device is powered on and in pairing mode
  ```

- **Model Loading**: If the model fails to load, verify that the model files are in the correct location. For TensorFlow Lite issues:
  ```
  # Check if TFLite is installed
  pip list | grep tflite

  # Install TFLite if missing
  pip install tflite-runtime
  ```

- **Raspberry Pi Performance**: If the API is slow or unresponsive:
  - Check CPU temperature: `vcgencmd measure_temp`
  - Check memory usage: `free -h`
  - Consider adding a small heatsink to the Raspberry Pi for better thermal performance
  - Reduce the number of Gunicorn workers to 1 in production mode if memory is limited

- **Detailed Logs**: Check the logs for detailed error messages and stack traces.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
