#!/usr/bin/env python3
"""
Flask API server for RespiraSense AI
This server exposes endpoints for sensor data collection and COPD prediction
"""

import os
import sys
import json
import logging
import asyncio
from pathlib import Path
from logging.handlers import RotatingFileHandler

# Configure logging first
# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Configure logging with more detailed format and rotating file handler
# Use smaller log files for Raspberry Pi
# Create handlers separately so we can set levels
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

info_file_handler = RotatingFileHandler(
    'logs/respirasense_api.log',
    maxBytes=5242880,  # 5MB (smaller for Raspberry Pi)
    backupCount=3,     # Keep 3 backup logs (reduced for Raspberry Pi)
    encoding='utf-8'
)
info_file_handler.setLevel(logging.INFO)

debug_file_handler = RotatingFileHandler(
    'logs/debug.log',
    maxBytes=5242880,  # 5MB
    backupCount=2,     # Keep 2 backup logs
    encoding='utf-8'
)
debug_file_handler.setLevel(logging.DEBUG)

# Set formatter for all handlers
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s')
console_handler.setFormatter(formatter)
info_file_handler.setFormatter(formatter)
debug_file_handler.setFormatter(formatter)

# Configure the root logger
logging.basicConfig(
    level=logging.DEBUG,
    handlers=[console_handler, info_file_handler, debug_file_handler]
)
logger = logging.getLogger(__name__)
logger.info("Starting RespiraSense AI API")

# Add the project root to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
# Insert at the beginning to ensure it's found first, but avoid conflicts with built-in modules
if current_dir not in sys.path:
    sys.path.append(current_dir)
logger.debug(f"Python path: {sys.path}")

# Check if __init__.py files exist in required directories, create if missing
required_dirs = [
    'hardware_code',
    'hardware_code/body_temp',
    'hardware_code/spirometry',
    'hardware_code/max30100',
    'hardware_code/buzzer',
    'hardware_code/whispering_pectoriloquy',
    'hardware_code/oximeter_hack',
    'model'
]

for directory in required_dirs:
    dir_path = os.path.join(current_dir, directory)
    init_file = os.path.join(dir_path, '__init__.py')
    if os.path.exists(dir_path) and not os.path.exists(init_file):
        print(f"Creating missing __init__.py in {directory}")
        with open(init_file, 'w') as f:
            f.write(f'"""\n{directory.split("/")[-1]} package\n"""\n')

# Now import the modules after adding the path
try:
    # Standard libraries
    import joblib
    import numpy as np
    from flask_cors import CORS
    from flask import Flask, request, jsonify

    # Try to import TensorFlow Lite (for Raspberry Pi)
    try:
        import tflite_runtime.interpreter as tflite
        USE_TFLITE = True
        print("Using TensorFlow Lite runtime")
    except ImportError:
        # Fall back to regular TensorFlow if TFLite is not available
        import tensorflow as tf
        USE_TFLITE = False
        print("Using regular TensorFlow")

    # Our modules - with fallbacks for missing hardware
    try:
        from hardware_code.body_temp.temperature import read_temperature
    except ImportError as e:
        print(f"Warning: Could not import temperature module: {e}")
        # Define a mock function

        def read_temperature():
            print("Using mock temperature function")
            return 36.5  # Return a normal body temperature

    try:
        from hardware_code.spirometry.mir_smart_one import perform_spirometry_test_bluetooth
    except ImportError as e:
        print(f"Warning: Could not import spirometry module: {e}")
        # Define a mock function

        async def perform_spirometry_test_bluetooth():
            print("Using mock spirometry function")
            return {
                "fev1": 3.0,
                "fvc": 4.0,
                "pef": 400,
                "fev1_fvc_ratio": 75
            }

    # Try to import UART oximeter module
    try:
        from hardware_code.oximeter_hack.read_SP02_uart_data import read_oximeter_data
    except ImportError as e:
        print(f"Warning: Could not import UART oximeter module: {e}")
        # Define a mock function

        def read_oximeter_data():
            print("Using mock oximeter function")
            return 98.0, 72.0  # Return normal SpO2 and heart rate
except ImportError as e:
    print(f"Import error: {e}")
    print(f"Current Python path: {sys.path}")
    print(f"Current directory: {current_dir}")
    print(f"Directory contents: {os.listdir(current_dir)}")
    if os.path.exists(os.path.join(current_dir, 'hardware_code')):
        print(
            f"Hardware code directory contents: {os.listdir(os.path.join(current_dir, 'hardware_code'))}")

    # Try to provide more helpful error messages
    if "No module named 'hardware_code'" in str(e):
        print(
            "\nERROR: The 'hardware_code' directory is not recognized as a Python package.")
        print("Make sure there's an __init__.py file in the hardware_code directory and all subdirectories.")
        print("Run the following commands to create them:")
        print("  touch hardware_code/__init__.py")
        print("  touch hardware_code/body_temp/__init__.py")
        print("  touch hardware_code/spirometry/__init__.py")
    elif "tensorflow" in str(e) or "numpy" in str(e) or "flask" in str(e):
        print("\nERROR: Missing required Python packages.")
        print("Make sure you've activated the conda environment and installed all dependencies:")
        print("  ./setup_conda_env.sh")

    sys.exit(1)

# Flask imports

# ML libraries

# Import sensor modules - these will be available after adding the project root to the path

# Logging is already configured at the top of the file

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Global variables
model = None
scaler = None


def load_model_and_scaler():
    """Load the COPD prediction model and scaler"""
    global model, scaler, USE_TFLITE, logger

    # Make sure logger is defined
    if 'logger' not in globals():
        logger = logging.getLogger(__name__)

    try:
        model_dir = Path(__file__).parent / 'model'
        model_path = model_dir / 'copd_model.h5'
        scaler_path = model_dir / 'copd_scaler.pkl'

        if not model_path.exists():
            logger.error(f"Model file not found at: {model_path}")
            return False

        if not scaler_path.exists():
            logger.error(f"Scaler file not found at: {scaler_path}")
            return False

        # Load the scaler
        try:
            scaler = joblib.load(str(scaler_path))
            logger.info("Scaler loaded successfully")
        except Exception as scaler_error:
            logger.error(f"Failed to load scaler: {scaler_error}")
            # Check if it's a sklearn import error
            if "sklearn" in str(scaler_error):
                logger.error(
                    "scikit-learn is required but not installed. Install with: pip install scikit-learn")
            raise scaler_error

        # Load the model based on available runtime
        if USE_TFLITE:
            # For Raspberry Pi, use TensorFlow Lite
            # Convert model to TFLite if needed (first run)
            tflite_model_path = model_dir / 'copd_model.tflite'

            if not tflite_model_path.exists():
                logger.info("Converting TensorFlow model to TFLite format...")
                # We need to use regular TensorFlow for conversion
                import tensorflow as tf
                converter = tf.lite.TFLiteConverter.from_keras_model(
                    tf.keras.models.load_model(str(model_path))
                )
                tflite_model = converter.convert()
                with open(str(tflite_model_path), 'wb') as f:
                    f.write(tflite_model)
                logger.info("Model converted to TFLite format")

            # Load the TFLite model
            interpreter = tflite.Interpreter(model_path=str(tflite_model_path))
            interpreter.allocate_tensors()

            # Get input and output tensors
            input_details = interpreter.get_input_details()
            output_details = interpreter.get_output_details()

            # Create a wrapper function to match the TensorFlow model API
            def tflite_predict(input_data, verbose=0):
                interpreter.set_tensor(
                    input_details[0]['index'], input_data.astype(np.float32))
                interpreter.invoke()
                output_data = interpreter.get_tensor(
                    output_details[0]['index'])
                return output_data

            # Store the predict function as our model
            model = type('TFLiteModel', (), {'predict': tflite_predict})()
            logger.info("TFLite model loaded successfully")
        else:
            # For more powerful systems, use regular TensorFlow
            model = tf.keras.models.load_model(str(model_path))
            logger.info("TensorFlow model loaded successfully")

        return True
    except Exception as e:
        logger.error(f"Error loading model or scaler: {str(e)}")
        logger.exception("Detailed error:")
        return False

# Routes


@app.route('/')
def index():
    """API root endpoint"""
    return jsonify({
        'name': 'RespiraSense AI API',
        'version': '1.0',
        'endpoints': {
            '/temperature': 'GET - Read body temperature',
            '/oximeter': 'GET - Read SpO2 and heart rate',
            '/spirometry': 'GET - Perform spirometry test',
            '/predict': 'POST - Predict COPD risk',
            '/health': 'GET - Check API health'
        }
    })


@app.route('/health')
def health_check():
    """Health check endpoint"""
    health_status = {
        'status': 'ok',
        'model_loaded': model is not None and scaler is not None
    }
    return jsonify(health_status)


@app.route('/temperature')
def get_temperature():
    """Get body temperature from MLX90614 sensor"""
    try:
        temp = read_temperature()
        if temp == -275:
            return jsonify({'error': 'Temperature sensor error'}), 500
        return jsonify({'temperature': float(temp)})
    except Exception as e:
        logger.error(f"Error reading temperature: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/oximeter')
def get_oximeter_data():
    """Get SpO2 and heart rate from UART-hacked oximeter"""
    try:
        spo2, heart_rate = read_oximeter_data()
        return jsonify({
            'spo2': float(spo2),
            'heart_rate': float(heart_rate),
            'sensor_type': 'uart_hack'
        })
    except Exception as e:
        logger.error(f"Error reading oximeter data: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/spirometry', methods=['GET', 'POST'])
def perform_spirometry():
    """Perform spirometry test using MIR Smart One device with optional demographics"""
    try:
        # Get demographics from request if provided
        age = None
        height_cm = None
        gender = None
        weight_kg = None

        if request.method == 'POST':
            data = request.get_json() or {}
            age = data.get('age')
            height_cm = data.get('height_cm')
            gender = data.get('gender')
            weight_kg = data.get('weight_kg')
        elif request.method == 'GET':
            # Support query parameters for GET requests
            age = request.args.get('age', type=int)
            height_cm = request.args.get('height_cm', type=float)
            gender = request.args.get('gender')
            weight_kg = request.args.get('weight_kg', type=float)

        # Run the spirometry test asynchronously with demographics
        results = asyncio.run(perform_spirometry_test_bluetooth(
            age=age, height_cm=height_cm, gender=gender, weight_kg=weight_kg))

        if results is None:
            return jsonify({'error': 'Spirometry test failed'}), 500
        return jsonify(results)
    except Exception as e:
        logger.error(f"Error performing spirometry test: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/predict', methods=['POST'])
def predict_copd():
    """Predict COPD risk using the trained model"""
    if model is None or scaler is None:
        return jsonify({'error': 'Model not loaded'}), 500

    try:
        # Get data from request
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Required fields for prediction
        required_fields = [
            'age', 'gen', 'ht', 'wt', 'smoke_hist', 'smoke_rt',
            'fev1', 'fvc', 'pefr', 'hr', 'spo2', 'temp',
            'wheeze', 'cough', 'sob', 'sputum', 'pectoriloquy',
            'exp_biomass', 'exp_occ', 'fam_hist'
        ]

        # Check if all required fields are present
        missing_fields = [
            field for field in required_fields if field not in data]
        if missing_fields:
            return jsonify({
                'error': 'Missing required fields',
                'missing_fields': missing_fields
            }), 400

        # Convert data to numpy array
        input_data = np.array([data[field]
                              for field in required_fields]).reshape(1, -1)

        # Scale the data
        scaled_data = scaler.transform(input_data)

        # Make prediction
        prediction = model.predict(scaled_data, verbose=0)
        probability = float(prediction[0][0])
        diagnosis = "Positive" if probability > 0.5 else "Negative"
        confidence = "High" if abs(probability - 0.5) > 0.3 else "Moderate"

        # Return prediction results
        return jsonify({
            'probability': probability,
            'diagnosis': diagnosis,
            'confidence': confidence
        })
    except Exception as e:
        logger.error(f"Error making prediction: {str(e)}")
        return jsonify({'error': str(e)}), 500


if __name__ == '__main__':
    # Load model and scaler
    try:
        load_model_and_scaler()
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        logger.info(
            "Continuing without model - prediction endpoint will not work")

    # Log startup information
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Running on: {os.uname().nodename}")

    # Run the Flask app - use threaded=False on Raspberry Pi for better performance
    # Disable debug mode to avoid name conflicts with 'code' module
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=False)
