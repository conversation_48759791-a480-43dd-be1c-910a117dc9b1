import numpy as np
import tensorflow as tf
import joblib
import os
from pathlib import Path


def load_model_and_scaler():
    current_dir = Path(__file__).parent.absolute()

    model_path = current_dir / 'copd_model.h5'
    scaler_path = current_dir / 'copd_scaler.pkl'

    if not model_path.exists():
        raise FileNotFoundError(f"Model file not found at: {model_path}")
    if not scaler_path.exists():
        raise FileNotFoundError(f"Scaler file not found at: {scaler_path}")

    try:
        model = tf.keras.models.load_model(str(model_path))
        scaler = joblib.load(str(scaler_path))
        return model, scaler
    except Exception as e:
        raise Exception(f"Error loading model or scaler: {str(e)}")


try:
    # Load model and scaler
    model, scaler = load_model_and_scaler()

    # Test data for likely COPD positive patient - matching exactly with training features
    test_data = {
        'age': 72,
        'gen': 0,
        'ht': 170,
        'wt': 65,
        'smoke_hist': 2,          # 0=never, 1=former, 2=current
        'smoke_rt': 30,
        'fev1': 1.2,
        'fvc': 2.8,
        'pefr': 200,
        'hr': 88,
        'spo2': 91,
        'temp': 37.1,
        'wheeze': 1,             # 1=yes, 0=no
        'cough': 1,
        'sob': 1,
        'sputum': 1,
        'pectoriloquy': 1,
        'exp_biomass': 1,
        'exp_occ': 1,
        'fam_hist': 1
    }

    # Convert to numpy array
    input_data = np.array(list(test_data.values())).reshape(1, -1)

    # Scale the data
    scaled_data = scaler.transform(input_data)

    # Make prediction
    prediction = model.predict(scaled_data, verbose=0)
    probability = prediction[0][0]
    diagnosis = "Positive" if probability > 0.5 else "Negative"

    # Print results
    print("\n=== COPD Risk Assessment (High Risk Case) ===")
    print("\nPatient Data:")
    print("--------------")
    print(f"Age: {test_data['age']} years")
    print(f"Gender: {'Female' if test_data['gen'] == 1 else 'Male'}")
    print(f"Height: {test_data['ht']} cm")
    print(f"Weight: {test_data['wt']} kg")
    print(
        f"Smoking: {'Never' if test_data['smoke_hist'] == 0 else 'Former' if test_data['smoke_hist'] == 1 else 'Current'}")
    print(f"Smoking Rate: {test_data['smoke_rt']} cigarettes/day")
    print(f"FEV1: {test_data['fev1']} L")
    print(f"FVC: {test_data['fvc']} L")
    print(f"PEFR: {test_data['pefr']} L/min")
    print(f"SpO2: {test_data['spo2']}%")
    print(f"Heart Rate: {test_data['hr']} bpm")
    print("Symptoms Present: " +
          ("Wheeze, " if test_data['wheeze'] else "") +
          ("Cough, " if test_data['cough'] else "") +
          ("Shortness of Breath, " if test_data['sob'] else "") +
          ("Sputum Production" if test_data['sputum'] else ""))

    print("\nPrediction Results:")
    print("------------------")
    print(f"COPD Risk: {probability:.1%}")
    print(f"Diagnosis: {diagnosis}")
    print(
        f"Confidence: {'High' if abs(probability - 0.5) > 0.3 else 'Moderate'}")

except FileNotFoundError as e:
    print(f"\nError: {str(e)}")
    print("Please ensure the model and scaler files are in the correct location.")
except Exception as e:
    print(f"\nAn error occurred: {str(e)}")
