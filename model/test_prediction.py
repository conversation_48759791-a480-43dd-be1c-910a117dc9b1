import numpy as np
import tensorflow as tf
import joblib
import os
from pathlib import Path


def load_model_and_scaler():
    current_dir = Path(__file__).parent.absolute()

    model_path = current_dir / 'copd_model.h5'
    scaler_path = current_dir / 'copd_scaler.pkl'

    if not model_path.exists():
        raise FileNotFoundError(f"Model file not found at: {model_path}")
    if not scaler_path.exists():
        raise FileNotFoundError(f"Scaler file not found at: {scaler_path}")

    try:
        model = tf.keras.models.load_model(str(model_path))
        scaler = joblib.load(str(scaler_path))
        return model, scaler
    except Exception as e:
        raise Exception(f"Error loading model or scaler: {str(e)}")


try:
    # Load model and scaler
    model, scaler = load_model_and_scaler()

    test_data = {
        'age': 65,
        'gen': 1,  # female
        'ht': 165,
        'wt': 70,
        'smoke_hist': 1,  # former smoker
        'smoke_rt': 15,
        'fev1': 1.8,
        'fvc': 2.9,
        'pefr': 280,
        'hr': 82,
        'spo2': 94,
        'temp': 36.8,
        'wheeze': 1,
        'cough': 1,
        'sob': 1,
        'sputum': 1,
        'pectoriloquy': 0,
        'exp_biomass': 0,
        'exp_occ': 1,
        'fam_hist': 0
    }

    # Convert to numpy array
    input_data = np.array(list(test_data.values())).reshape(1, -1)

    # Scale the data
    scaled_data = scaler.transform(input_data)

    # Make prediction
    prediction = model.predict(scaled_data, verbose=0)
    probability = prediction[0][0]
    diagnosis = "Positive" if probability > 0.5 else "Negative"

    # Print results
    print("\n=== COPD Risk Assessment ===")
    print("\nPatient Data:")
    print("--------------")
    print(f"Age: {test_data['age']} years")
    print(f"Gender: {'Female' if test_data['gen'] == 1 else 'Male'}")
    print(f"Height: {test_data['ht']} cm")
    print(f"Weight: {test_data['wt']} kg")
    print(
        f"Smoking: {'Never' if test_data['smoke_hist'] == 0 else 'Former' if test_data['smoke_hist'] == 1 else 'Current'}")
    print(f"FEV1: {test_data['fev1']} L")
    print(f"FVC: {test_data['fvc']} L")
    print(f"SpO2: {test_data['spo2']}%")
    print(f"Heart Rate: {test_data['hr']} bpm")

    print("\nPrediction Results:")
    print("------------------")
    print(f"COPD Risk: {probability:.1%}")
    print(f"Diagnosis: {diagnosis}")
    print(
        f"Confidence: {'High' if abs(probability - 0.5) > 0.3 else 'Moderate'}")

except FileNotFoundError as e:
    print(f"\nError: {str(e)}")
    print("File not found")
except Exception as e:
    print(f"\nAn error occurred: {str(e)}")
