# mir_ble/decoder.py

import struct


class MirSpiroDecoder:
    @staticmethod
    def decode_fev1(data: bytes) -> float | None:
        if len(data) < 2:
            return None
        raw_val = struct.unpack('<H', data[:2])[0]
        if raw_val == 0xFFFF:
            return None
        return raw_val / 100.0

    @staticmethod
    def decode_fvc(data: bytes) -> float | None:
        if len(data) < 2:
            return None
        raw_val = struct.unpack('<H', data[:2])[0]
        if raw_val == 0xFFFF:
            return None
        return raw_val / 100.0

    @staticmethod
    def decode_pef(data: bytes) -> float | None:
        if len(data) < 2:
            return None
        raw_val = struct.unpack('<H', data[:2])[0]
        if raw_val == 0xFFFF:
            return None
        return raw_val / 10.0

    @staticmethod
    def decode_fev1_fvc_ratio(data: bytes) -> float | None:
        if len(data) == 0:
            return None
        if len(data) == 1:
            return float(data[0])
        elif len(data) >= 2:
            return float(struct.unpack('<H', data[:2])[0])
        return None

    @staticmethod
    def decode_status(data: bytes) -> str:
        if len(data) < 1:
            return "Unknown"
        status_map = {
            0x00: "Idle",
            0x01: "Measuring",
            0x02: "Test complete"
        }
        return status_map.get(data[0], f"Unknown ({data[0]})")
