# mir_ble/utils.py

import asyncio


async def wait_for_condition(condition_fn, timeout=10, interval=0.5):
    """
    Wait asynchronously for a condition function to become True.

    :param condition_fn: A callable returning bool.
    :param timeout: Maximum seconds to wait.
    :param interval: Seconds between checks.
    :return: True if condition met within timeout, False otherwise.
    """
    end_time = asyncio.get_event_loop().time() + timeout
    while True:
        if condition_fn():
            return True
        if asyncio.get_event_loop().time() >= end_time:
            return False
        await asyncio.sleep(interval)
