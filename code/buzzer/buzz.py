# Buzz function

import RPi.GPIO as GPIO
from time import sleep

# VCC output pin for the buzzer
outputPin = 40

GPIO.setmode(GPIO.BOARD)
GPIO.setup(outputPin, GPIO.OUT)


def beep(n):
    for i in range(n):
        GPIO.output(outputPin, 1)
        sleep(0.5)
        GPIO.output(outputPin, 0)
        sleep(0.1)
        GPIO.output(outputPin, 1)
        sleep(0.5)
        GPIO.output(outputPin, 0)
        sleep(0.5)
    if __name__ != "__main__":
        GPIO.cleanup()


if __name__ == "__main__":
    beep(3)
    GPIO.cleanup()
