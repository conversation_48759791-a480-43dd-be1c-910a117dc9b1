import time
import board
import busio
import adafruit_mpu6050
from statistics import mean
import numpy as np
from scipy.signal import find_peaks

class BreathingSensor:
    def __init__(self):
        i2c = busio.I2C(board.SCL, board.SDA)
        self.mpu = adafruit_mpu6050.MPU6050(i2c)
        self.sample_window = 30  # 30 seconds measurement window
        self.sample_rate = 10    # 10Hz sampling rate
        
    def measure_breathing_rate(self):
        samples = []
        start_time = time.time()
        
        # Collect acceleration data for sample_window seconds
        while time.time() - start_time < self.sample_window:
            # Get vertical acceleration (z-axis) for chest movement
            accel = self.mpu.acceleration[2]
            samples.append(accel)
            time.sleep(1/self.sample_rate)
            
        # Process the signal to find peaks (breaths)
        samples = np.array(samples)
        peaks, _ = find_peaks(samples, distance=self.sample_rate)  # Minimum distance between peaks
        
        # Calculate breathing rate (breaths per minute)
        num_breaths = len(peaks)
        breathing_rate = (num_breaths * 60) / self.sample_window
        
        return round(breathing_rate, 1)

def read_breathing_rate():
    try:
        sensor = BreathingSensor()
        rate = sensor.measure_breathing_rate()
        return rate
    except Exception as e:
        print(f"Error reading breathing rate: {e}")
        return -1

if __name__ == "__main__":
    try:
        while True:
            rate = read_breathing_rate()
            if rate != -1:
                print(f"Breathing rate: {rate} breaths/min")
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nExiting...")